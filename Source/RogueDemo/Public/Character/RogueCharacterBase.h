// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "AbilitySystemInterface.h"
#include "GenericTeamAgentInterface.h"
#include "GameFramework/Character.h"
#include "Interaction/CombatInterface.h"
#include "RogueCharacterBase.generated.h"

struct FGameplayTag;
class UGameplayEffect;
struct FOnAttributeChangeData;
class URogueWidgetComponentBase;
class AWeaponBase;
class URogueAbilitySlotComponent;
class URoguePawnData;
class URogueAttributeSet;
class URogueAbilitySystemComponent;
class UAttributeSet;
class UCombatComponent;

USTRUCT()
struct FCharacterWeaponData
{
	GENERATED_BODY()

	UPROPERTY(EditDefaultsOnly)
	TSubclassOf<AWeaponBase> WeaponClass = nullptr;

	UPROPERTY(EditDefaultsOnly)
	FName SocketName = NAME_None;

	UPROPERTY(EditDefaultsOnly)
	FTransform OffsetTransform = FTransform::Identity;
};

UCLASS()
class ROGUEDEMO_API ARogueCharacterBase : public ACharacter, public IAbilitySystemInterface, public ICombatInterface,
                                          public IGenericTeamAgentInterface
{
	GENERATED_BODY()

public:
	ARogueCharacterBase();

	virtual UAbilitySystemComponent* GetAbilitySystemComponent() const override;
	virtual UAttributeSet* GetAttributeSet() const;
	virtual URogueAbilitySystemComponent* GetRogueASC();
	virtual URogueAttributeSet* GetRogueAS() const;

	/** ~begin CombatInterface **/
	virtual void EnableWeaponCollision_Implementation() override;
	virtual void DisableWeaponCollision_Implementation() override;
	virtual void Die_Implementation() override;
	virtual int32 GetLevel_Implementation() override;
	/** ~end CombatInterface **/

	/** ~begin IGenericTeamAgentInterface **/
	virtual void SetGenericTeamId(const FGenericTeamId& TeamID) override;
	/** ~end IGenericTeamAgentInterface **/

	UPROPERTY(Replicated, BlueprintReadOnly, Category = "Movement")
	bool bSprinting = false;
protected:
	virtual void BeginPlay() override;

	/** ~begin AActor interface **/
	virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;
	/** ~end AActor interface **/

	virtual void GiveStartupAbilities();
	virtual void GiveStartupAttributes(TSubclassOf<UGameplayEffect> EffectClassOverride = nullptr);
	virtual void BindASCDelegates();

	virtual void ReCalculateMaxWalkSpeed();


	/** ~begin Attribute change event **/
	virtual void OnHealthChange(const FOnAttributeChangeData& Data);
	virtual void OnShieldChange(const FOnAttributeChangeData& Data);
	virtual void OnBonusHealthChange(const FOnAttributeChangeData& Data);
	virtual void OnMoveSpeedChange(const FOnAttributeChangeData& Data);
	/** ~end Attribute change event **/

	/** ~begin GameplayTag change event **/
	virtual void OnSprintTagChange(const FGameplayTag Tag, int32 NewCount);
	/** ~end GameplayTag change event **/

	/** ~begin for GAS **/
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly)
	TObjectPtr<URogueAbilitySlotComponent> AbilitySlotComponent;
	UPROPERTY()
	TObjectPtr<UAbilitySystemComponent> AbilitySystemComponent;
	UPROPERTY()
	TObjectPtr<UAttributeSet> AttributeSet;
	/** ~end for GAS **/

	UPROPERTY(EditDefaultsOnly, Category="Pawn Data")
	TObjectPtr<URoguePawnData> RoguePawnData;

	UPROPERTY(VisibleInstanceOnly, Category="Weapon")
	TArray<TObjectPtr<AWeaponBase>> EquippedWeapons;

	UPROPERTY(VisibleAnywhere, Category = "UI")
	TObjectPtr<URogueWidgetComponentBase> InGameWidget;

	UPROPERTY(EditAnywhere, Category = "Status")
	int32 Level = 1;

	UPROPERTY(Replicated)
	FGenericTeamId MyTeamID;

private:
	virtual void InitAbilityActorInfo()
	{
	}

	void ApplyVitalAttribute();
	void SpawnWeapons();
	void EquipWeapon(const FCharacterWeaponData& WeaponData);
	void UnequipWeapon();

	void HandleHealthChange(const FOnAttributeChangeData& Data);
	void HandleShieldChange(const FOnAttributeChangeData& Data);
	void HandleBonusHealthChange(const FOnAttributeChangeData& Data);
};

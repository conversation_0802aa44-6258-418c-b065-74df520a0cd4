// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "AbilitySystemComponent.h"
#include "AttributeSet.h"
#include "RogueAttributeSet.generated.h"

USTRUCT(BlueprintType)
struct FEffectProperties
{
	GENERATED_BODY()

	FEffectProperties()
	{
	}

	UPROPERTY(VisibleAnywhere)
	UAbilitySystemComponent* SourceASC = nullptr;
	UPROPERTY(VisibleAnywhere)
	UAbilitySystemComponent* TargetASC = nullptr;
	UPROPERTY(VisibleAnywhere)
	AActor* SourceAvatarActor = nullptr;
	UPROPERTY(VisibleAnywhere)
	AActor* TargetAvatarActor = nullptr;
	UPROPERTY(VisibleAnywhere)
	AController* SourceController = nullptr;
	UPROPERTY(VisibleAnywhere)
	AController* TargetController = nullptr;
	UPROPERTY(VisibleAnywhere)
	ACharacter* SourceCharacter = nullptr;
	UPROPERTY(VisibleAnywhere)
	ACharacter* TargetCharacter = nullptr;
	UPROPERTY(VisibleAnywhere)
	FGameplayEffectContextHandle ContextHandle;
};


#define ATTRIBUTE_ACCESSORS(ClassName, PropertyName) \
GAMEPLAYATTRIBUTE_PROPERTY_GETTER(ClassName, PropertyName) \
GAMEPLAYATTRIBUTE_VALUE_GETTER(PropertyName) \
GAMEPLAYATTRIBUTE_VALUE_SETTER(PropertyName) \
GAMEPLAYATTRIBUTE_VALUE_INITTER(PropertyName)

class URogueAbilitySystemComponent;
/**
 * 
 */
UCLASS()
class ROGUEDEMO_API URogueAttributeSet : public UAttributeSet
{
	GENERATED_BODY()

public:
	URogueAttributeSet();
	
	ATTRIBUTE_ACCESSORS(URogueAttributeSet, Health);
	ATTRIBUTE_ACCESSORS(URogueAttributeSet, Shield);
	ATTRIBUTE_ACCESSORS(URogueAttributeSet, BonusHealth);
	ATTRIBUTE_ACCESSORS(URogueAttributeSet, MaxHealth);
	ATTRIBUTE_ACCESSORS(URogueAttributeSet, MaxShield);
	ATTRIBUTE_ACCESSORS(URogueAttributeSet, Damage);
	ATTRIBUTE_ACCESSORS(URogueAttributeSet, AttackSpeed);
	ATTRIBUTE_ACCESSORS(URogueAttributeSet, MoveSpeed);
	ATTRIBUTE_ACCESSORS(URogueAttributeSet, CriticalChance);
	ATTRIBUTE_ACCESSORS(URogueAttributeSet, BlockChance);
	ATTRIBUTE_ACCESSORS(URogueAttributeSet, SprintSpeed);
	ATTRIBUTE_ACCESSORS(URogueAttributeSet, LifeStealOnHit);
	ATTRIBUTE_ACCESSORS(URogueAttributeSet, LifeStealOnCritical);
	ATTRIBUTE_ACCESSORS(URogueAttributeSet, HealthRegenerationRate);
	ATTRIBUTE_ACCESSORS(URogueAttributeSet, ShieldRegenerationRate);
	ATTRIBUTE_ACCESSORS(URogueAttributeSet, IncomingDamage);

protected:
	/* ~begin UAttributeSet interface */
	virtual void GetLifetimeReplicatedProps(TArray<class FLifetimeProperty>& OutLifetimeProps) const override;
	virtual void PreAttributeChange(const FGameplayAttribute& Attribute, float& NewValue) override;
	virtual void PreAttributeBaseChange(const FGameplayAttribute& Attribute, float& NewValue) const override;
	virtual void PostAttributeChange(const FGameplayAttribute& Attribute, float OldValue, float NewValue) override;
	virtual void PostGameplayEffectExecute(const FGameplayEffectModCallbackData& Data) override;
	/* ~end UAttributeSet interface */

	UFUNCTION()
	void OnRep_Health(const FGameplayAttributeData& OldValue);
	UFUNCTION()
	void OnRep_Shield(const FGameplayAttributeData& OldValue);
	UFUNCTION()
	void OnRep_BonusHealth(const FGameplayAttributeData& OldValue);
	UFUNCTION()
	void OnRep_MaxHealth(const FGameplayAttributeData& OldValue);
	UFUNCTION()
	void OnRep_MaxShield(const FGameplayAttributeData& OldValue);
	UFUNCTION()
	void OnRep_Damage(const FGameplayAttributeData& OldValue);
	UFUNCTION()
	void OnRep_AttackSpeed(const FGameplayAttributeData& OldValue);
	UFUNCTION()
	void OnRep_MoveSpeed(const FGameplayAttributeData& OldValue);
	UFUNCTION()
	void OnRep_CriticalChance(const FGameplayAttributeData& OldValue);
	UFUNCTION()
	void OnRep_BlockChance(const FGameplayAttributeData& OldValue);
	UFUNCTION()
	void OnRep_SprintSpeed(const FGameplayAttributeData& OldValue);
	UFUNCTION()
	void OnRep_LifeStealOnHit(const FGameplayAttributeData& OldValue);
	UFUNCTION()
	void OnRep_LifeStealOnCritical(const FGameplayAttributeData& OldValue);
	UFUNCTION()
	void OnRep_HealthRegenerationRate(const FGameplayAttributeData& OldValue);
	UFUNCTION()
	void OnRep_ShieldRegenerationRate(const FGameplayAttributeData& OldValue);
	UFUNCTION()
	void OnRep_IncomingDamage(const FGameplayAttributeData& OldValue);


	URogueAbilitySystemComponent* GetRogueASC() const;

	UPROPERTY()
	FActiveGameplayEffectHandle ShieldRegenHandle;
	UPROPERTY()
	FActiveGameplayEffectHandle HealthRegenHandle;
	UPROPERTY()
	FActiveGameplayEffectHandle BonusHealthDegenHandle;

private:
	void ClampAttribute(const FGameplayAttribute& Attribute, float& NewValue) const;
	void SetEffectProperties(const FGameplayEffectModCallbackData& Data, FEffectProperties& Props);
	void HandleShieldChange(const FGameplayEffectModCallbackData& Data);
	void HandleHealthChange(const FGameplayEffectModCallbackData& Data);
	void HandleBonusHealthChange(const FGameplayEffectModCallbackData& Data);

	/** ~Vital Attribute **/
	UPROPERTY(ReplicatedUsing = OnRep_Health)
	FGameplayAttributeData Health;
	UPROPERTY(ReplicatedUsing = OnRep_Shield)
	FGameplayAttributeData Shield;
	UPROPERTY(ReplicatedUsing = OnRep_BonusHealth)
	FGameplayAttributeData BonusHealth;

	/** ~Primary Attribute **/
	UPROPERTY(ReplicatedUsing = OnRep_MaxHealth)
	FGameplayAttributeData MaxHealth;
	UPROPERTY(ReplicatedUsing = OnRep_MaxShield)
	FGameplayAttributeData MaxShield;
	UPROPERTY(ReplicatedUsing = OnRep_Damage)
	FGameplayAttributeData Damage;
	UPROPERTY(ReplicatedUsing = OnRep_AttackSpeed)
	FGameplayAttributeData AttackSpeed;
	UPROPERTY(ReplicatedUsing = OnRep_MoveSpeed)
	FGameplayAttributeData MoveSpeed;
	UPROPERTY(ReplicatedUsing = OnRep_CriticalChance)
	FGameplayAttributeData CriticalChance;
	UPROPERTY(ReplicatedUsing = OnRep_BlockChance)
	FGameplayAttributeData BlockChance;
	UPROPERTY(ReplicatedUsing = OnRep_SprintSpeed)
	FGameplayAttributeData SprintSpeed;
	UPROPERTY(ReplicatedUsing = OnRep_LifeStealOnHit)
	FGameplayAttributeData LifeStealOnHit;
	UPROPERTY(ReplicatedUsing = OnRep_LifeStealOnCritical)
	FGameplayAttributeData LifeStealOnCritical;
	UPROPERTY(ReplicatedUsing = OnRep_HealthRegenerationRate)
	FGameplayAttributeData HealthRegenerationRate;
	UPROPERTY(ReplicatedUsing = OnRep_ShieldRegenerationRate)
	FGameplayAttributeData ShieldRegenerationRate;

	/** ~Meta Attribute **/
	UPROPERTY(ReplicatedUsing = OnRep_IncomingDamage)
	FGameplayAttributeData IncomingDamage;
};

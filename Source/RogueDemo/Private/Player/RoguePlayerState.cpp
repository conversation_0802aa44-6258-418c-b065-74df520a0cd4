// Fill out your copyright notice in the Description page of Project Settings.


#include "Player/RoguePlayerState.h"

#include "AbilitySystem/RogueAbilitySystemComponent.h"
#include "AbilitySystem/RogueAttributeSet.h"

ARoguePlayerState::ARoguePlayerState()
{
	SetNetUpdateFrequency(100);

	AbilitySystemComponent = CreateDefaultSubobject<URogueAbilitySystemComponent>("AbilitySystemComponent");
	AbilitySystemComponent->SetIsReplicated(true);
	AbilitySystemComponent->SetReplicationMode(EGameplayEffectReplicationMode::Mixed);

	AttributeSet = CreateDefaultSubobject<URogueAttributeSet>("AttributeSet");
}

UAbilitySystemComponent* ARoguePlayerState::GetAbilitySystemComponent() const
{
	return AbilitySystemComponent;
}

URogueAbilitySystemComponent* ARoguePlayerState::GetRogueASC() const
{
	return CastChecked<URogueAbilitySystemComponent>(GetAbilitySystemComponent());
}

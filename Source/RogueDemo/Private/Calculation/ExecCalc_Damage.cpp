// Fill out your copyright notice in the Description page of Project Settings.


#include "Calculation/ExecCalc_Damage.h"

#include "AbilitySystem/RogueAbilitySystemComponent.h"
#include "AbilitySystem/RogueAbilitySystemLibrary.h"
#include "AbilitySystem/RogueAbilityTypes.h"
#include "AbilitySystem/RogueAttributeSet.h"
#include "Game/RogueGameplayTags.h"

struct FDamageStatics
{
	FGameplayEffectAttributeCaptureDefinition CriticalChance;
	FGameplayEffectAttributeCaptureDefinition BlockChance;
	FGameplayEffectAttributeCaptureDefinition Health;
	FGameplayEffectAttributeCaptureDefinition Shield;
	FGameplayEffectAttributeCaptureDefinition BonusHealth;
	FGameplayEffectAttributeCaptureDefinition LifeStealOnHit;
	FGameplayEffectAttributeCaptureDefinition LifeStealOnCritical;

	FDamageStatics()
	{
		CriticalChance = FGameplayEffectAttributeCaptureDefinition(
			URogueAttributeSet::GetCriticalChanceAttribute(),
			EGameplayEffectAttributeCaptureSource::Source,
			true);

		BlockChance = FGameplayEffectAttributeCaptureDefinition(
			URogueAttributeSet::GetBlockChanceAttribute(),
			EGameplayEffectAttributeCaptureSource::Target,
			true);

		Health = FGameplayEffectAttributeCaptureDefinition(
			URogueAttributeSet::GetHealthAttribute(),
			EGameplayEffectAttributeCaptureSource::Target,
			true);

		Shield = FGameplayEffectAttributeCaptureDefinition(
			URogueAttributeSet::GetShieldAttribute(),
			EGameplayEffectAttributeCaptureSource::Target,
			true);

		BonusHealth = FGameplayEffectAttributeCaptureDefinition(
			URogueAttributeSet::GetBonusHealthAttribute(),
			EGameplayEffectAttributeCaptureSource::Target,
			true);

		LifeStealOnHit = FGameplayEffectAttributeCaptureDefinition(
			URogueAttributeSet::GetLifeStealOnHitAttribute(),
			EGameplayEffectAttributeCaptureSource::Source,
			true);

		LifeStealOnCritical = FGameplayEffectAttributeCaptureDefinition(
			URogueAttributeSet::GetLifeStealOnCriticalAttribute(),
			EGameplayEffectAttributeCaptureSource::Source,
			true);
	}
};

static FDamageStatics& DamageStatics()
{
	static FDamageStatics DmgStatics;
	return DmgStatics;
}

UExecCalc_Damage::UExecCalc_Damage()
{
	RelevantAttributesToCapture.Add(DamageStatics().CriticalChance);
	RelevantAttributesToCapture.Add(DamageStatics().BlockChance);
	RelevantAttributesToCapture.Add(DamageStatics().Health);
	RelevantAttributesToCapture.Add(DamageStatics().Shield);
	RelevantAttributesToCapture.Add(DamageStatics().BonusHealth);
	RelevantAttributesToCapture.Add(DamageStatics().LifeStealOnHit);
	RelevantAttributesToCapture.Add(DamageStatics().LifeStealOnCritical);
}

void UExecCalc_Damage::Execute_Implementation(const FGameplayEffectCustomExecutionParameters& ExecutionParams,
                                              FGameplayEffectCustomExecutionOutput& OutExecutionOutput) const
{
#if WITH_SERVER_CODE
	const FGameplayEffectSpec& Spec = ExecutionParams.GetOwningSpec();
	FRogueGameplayEffectContext* EffectContext = static_cast<FRogueGameplayEffectContext*>(Spec.GetContext().Get());
	check(EffectContext);

	const FGameplayTagContainer* CapturedSourceTags = Spec.CapturedSourceTags.GetAggregatedTags();
	const FGameplayTagContainer* CapturedTargetTags = Spec.CapturedTargetTags.GetAggregatedTags();

	FAggregatorEvaluateParameters EvaluateParameters;
	EvaluateParameters.SourceTags = CapturedSourceTags;
	EvaluateParameters.TargetTags = CapturedTargetTags;

	UAbilitySystemComponent* TargetASC = ExecutionParams.GetTargetAbilitySystemComponent();
	UAbilitySystemComponent* SourceASC = ExecutionParams.GetSourceAbilitySystemComponent();

	float BaseDamage = Spec.GetSetByCallerMagnitude(RogueGameplayTags::MetaAttribute_Damage, true, 0.f);

	float CriticalChance = 0.f;
	ExecutionParams.AttemptCalculateCapturedAttributeMagnitude(
		DamageStatics().CriticalChance, EvaluateParameters,
		CriticalChance);

	float BlockChance = 0.f;
	ExecutionParams.AttemptCalculateCapturedAttributeMagnitude(
		DamageStatics().BlockChance, EvaluateParameters,
		BlockChance);

	//
	// Calculate the final damage
	//
	float FinalDamage = BaseDamage;
	bool bCritical = FMath::FRandRange(0, 100.f) <= CriticalChance;
	if (bCritical)
	{
		FinalDamage *= 2.0f;
		EffectContext->SetIsCriticalHit(true);
	}
	if (FMath::FRandRange(0, 100.f) <= BlockChance)
	{
		FinalDamage = 0.f;
		EffectContext->SetIsBlockedHit(true);
	}

	//
	// Calculate Remaining Health
	//
	float BonusHealth = 0.f;
	ExecutionParams.AttemptCalculateCapturedAttributeMagnitude(
		DamageStatics().BonusHealth, EvaluateParameters,
		BonusHealth);
	float Shield = 0.f;
	ExecutionParams.AttemptCalculateCapturedAttributeMagnitude(
		DamageStatics().Shield, EvaluateParameters,
		Shield);
	float Health = 0.f;
	ExecutionParams.AttemptCalculateCapturedAttributeMagnitude(
		DamageStatics().Health, EvaluateParameters,
		Health);

	float RemainingDamage = FinalDamage;

	float DeltaBonus = -FMath::Min(BonusHealth, RemainingDamage);
	RemainingDamage -= FMath::Abs(DeltaBonus);

	float DeltaShield = -FMath::Min(Shield, RemainingDamage);
	RemainingDamage -= FMath::Abs(DeltaShield);

	float DeltaHealth = -FMath::Min(Health, RemainingDamage);

	if (!FMath::IsNearlyZero(DeltaBonus))
		OutExecutionOutput.AddOutputModifier(
			FGameplayModifierEvaluatedData(URogueAttributeSet::GetBonusHealthAttribute(), EGameplayModOp::Additive,
			                               DeltaBonus));

	if (!FMath::IsNearlyZero(DeltaShield))
		OutExecutionOutput.AddOutputModifier(
			FGameplayModifierEvaluatedData(URogueAttributeSet::GetShieldAttribute(), EGameplayModOp::Additive,
			                               DeltaShield));

	if (!FMath::IsNearlyZero(DeltaHealth))
		OutExecutionOutput.AddOutputModifier(
			FGameplayModifierEvaluatedData(URogueAttributeSet::GetHealthAttribute(), EGameplayModOp::Additive,
			                               DeltaHealth));

	//
	// 处理与伤害计算无关的内容
	// 
	if (FinalDamage > 0.f)
	{
		// ==添加Tag代表角色刚刚受到过攻击
		URogueAbilitySystemLibrary::ApplyDamagedRecentlyEffect(Cast<URogueAbilitySystemComponent>(TargetASC));

		// ==处理暴击吸血
		if (bCritical)
		{
			float LifeStealOnCritical = 0.f;
			ExecutionParams.AttemptCalculateCapturedAttributeMagnitude(
				DamageStatics().LifeStealOnCritical, EvaluateParameters,
				LifeStealOnCritical);
			if (LifeStealOnCritical > 0.f)
			{
				URogueAbilitySystemLibrary::ApplyHealInstantEffect(Cast<URogueAbilitySystemComponent>(SourceASC),
				                                                   LifeStealOnCritical);
			}
		}

		// ==处理普通攻击吸血
		float LifeStealOnHit = 0.f;
		ExecutionParams.AttemptCalculateCapturedAttributeMagnitude(
			DamageStatics().LifeStealOnHit, EvaluateParameters,
			LifeStealOnHit);
		if (LifeStealOnHit > 0.f)
		{
			URogueAbilitySystemLibrary::ApplyHealInstantEffect(Cast<URogueAbilitySystemComponent>(SourceASC),
			                                                   LifeStealOnHit);
		}
	}

#endif
}

// Fill out your copyright notice in the Description page of Project Settings.


#include "AbilitySystem/Ability/RogueGameplayAbility.h"

#include "RogueTypes.h"
#include "AbilitySystem/RogueAbilitySystemLibrary.h"
#include "AbilitySystem/RogueAttributeSet.h"
#include "Character/RogueCharacterBase.h"

URogueGameplayAbility::URogueGameplayAbility()
{
	NetExecutionPolicy = EGameplayAbilityNetExecutionPolicy::LocalPredicted;
	InstancingPolicy = EGameplayAbilityInstancingPolicy::InstancedPerActor;
}

void URogueGameplayAbility::ApplyDamageInstantToActor(AActor* TargetActor)
{
	FRogueDamageParams DamageParams;

	ARogueCharacterBase* RogueCharacter = Cast<ARogueCharacterBase>(GetAvatarActorFromActorInfo());
	const float DamageMultiplier = RogueCharacter->GetRogueAS()->GetDamage();

	URogueAbilitySystemLibrary::MakeDamageParams(Damage * DamageMultiplier, TargetActor,
	                                             GetAvatarActorFromActorInfo(), DamageParams);

	URogueAbilitySystemLibrary::ApplyDamageInstant(DamageParams);
}

void URogueGameplayAbility::ActivateAbility(const FGameplayAbilitySpecHandle Handle,
                                            const FGameplayAbilityActorInfo* ActorInfo,
                                            const FGameplayAbilityActivationInfo ActivationInfo,
                                            const FGameplayEventData* TriggerEventData)
{
	Super::ActivateAbility(Handle, ActorInfo, ActivationInfo, TriggerEventData);

	UE_LOG(LogTemp, Warning,
	       TEXT("GA ActivateAbility, Policy=%s, Role=%s"),
	       *UEnum::GetValueAsString(NetExecutionPolicy),
	       *UEnum::GetValueAsString(GetAvatarActorFromActorInfo()->GetLocalRole()));
}

float URogueGameplayAbility::GetAttackSpeed() const
{
	ARogueCharacterBase* RogueCharacter = Cast<ARogueCharacterBase>(GetAvatarActorFromActorInfo());
	return RogueCharacter->GetRogueAS()->GetAttackSpeed();
}

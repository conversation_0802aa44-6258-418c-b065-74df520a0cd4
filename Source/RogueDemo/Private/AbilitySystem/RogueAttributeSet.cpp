// Fill out your copyright notice in the Description page of Project Settings.


#include "AbilitySystem/RogueAttributeSet.h"

#include "AbilitySystemBlueprintLibrary.h"
#include "AbilitySystemComponent.h"
#include "GameplayEffectExtension.h"
#include "AbilitySystem/RogueAbilitySystemComponent.h"
#include "AbilitySystem/RogueAbilitySystemLibrary.h"
#include "GameFramework/Character.h"
#include "Net/UnrealNetwork.h"

URogueAttributeSet::URogueAttributeSet()
	: Health(100.f),
	MaxHealth(100.f)
{
}

void URogueAttributeSet::GetLifetimeReplicatedProps(TArray<class FLifetimeProperty>& OutLifetimeProps) const
{
	Super::GetLifetimeReplicatedProps(OutLifetimeProps);

	DOREPLIFETIME_CONDITION_NOTIFY(URogueAttributeSet, Health, COND_None, REPNOTIFY_Always);
	DOREPLIFETIME_CONDITION_NOTIFY(URogueAttributeSet, Shield, COND_None, REPNOTIFY_Always);
	DOREPLIFETIME_CONDITION_NOTIFY(URogueAttributeSet, BonusHealth, COND_None, REPNOTIFY_Always);
	DOREPLIFETIME_CONDITION_NOTIFY(URogueAttributeSet, MaxHealth, COND_None, REPNOTIFY_Always);
	DOREPLIFETIME_CONDITION_NOTIFY(URogueAttributeSet, MaxShield, COND_None, REPNOTIFY_Always);
	DOREPLIFETIME_CONDITION_NOTIFY(URogueAttributeSet, Damage, COND_None, REPNOTIFY_Always);
	DOREPLIFETIME_CONDITION_NOTIFY(URogueAttributeSet, AttackSpeed, COND_None, REPNOTIFY_Always);
	DOREPLIFETIME_CONDITION_NOTIFY(URogueAttributeSet, MoveSpeed, COND_None, REPNOTIFY_Always);
	DOREPLIFETIME_CONDITION_NOTIFY(URogueAttributeSet, CriticalChance, COND_None, REPNOTIFY_Always);
	DOREPLIFETIME_CONDITION_NOTIFY(URogueAttributeSet, BlockChance, COND_None, REPNOTIFY_Always);
	DOREPLIFETIME_CONDITION_NOTIFY(URogueAttributeSet, SprintSpeed, COND_None, REPNOTIFY_Always);
	DOREPLIFETIME_CONDITION_NOTIFY(URogueAttributeSet, LifeStealOnHit, COND_None, REPNOTIFY_Always);
	DOREPLIFETIME_CONDITION_NOTIFY(URogueAttributeSet, LifeStealOnCritical, COND_None, REPNOTIFY_Always);
	DOREPLIFETIME_CONDITION_NOTIFY(URogueAttributeSet, HealthRegenerationRate, COND_None, REPNOTIFY_Always);
	DOREPLIFETIME_CONDITION_NOTIFY(URogueAttributeSet, ShieldRegenerationRate, COND_None, REPNOTIFY_Always);
	DOREPLIFETIME_CONDITION_NOTIFY(URogueAttributeSet, IncomingDamage, COND_None, REPNOTIFY_Always);
}

void URogueAttributeSet::PreAttributeChange(const FGameplayAttribute& Attribute, float& NewValue)
{
	Super::PreAttributeChange(Attribute, NewValue);

	ClampAttribute(Attribute, NewValue);
}

void URogueAttributeSet::PreAttributeBaseChange(const FGameplayAttribute& Attribute, float& NewValue) const
{
	Super::PreAttributeBaseChange(Attribute, NewValue);

	ClampAttribute(Attribute, NewValue);
}

void URogueAttributeSet::PostAttributeChange(const FGameplayAttribute& Attribute, float OldValue, float NewValue)
{
	Super::PostAttributeChange(Attribute, OldValue, NewValue);
	URogueAbilitySystemComponent* RogueASC = GetRogueASC();
	check(RogueASC);
	/** Prevent health from being greater than max health **/
	if (Attribute == GetMaxHealthAttribute())
	{
		if (GetHealth() > NewValue)
		{
			RogueASC->ApplyModToAttribute(GetHealthAttribute(), EGameplayModOp::Override, NewValue);
		}
		if (GetBonusHealth() > NewValue)
		{
			RogueASC->ApplyModToAttribute(GetBonusHealthAttribute(), EGameplayModOp::Override, NewValue);
		}
		if (GetMaxShield() > NewValue)
		{
			RogueASC->ApplyModToAttribute(GetMaxShieldAttribute(), EGameplayModOp::Override, NewValue);
		}
	}
	else if (Attribute == GetMaxShieldAttribute())
	{
		if (GetShield() > NewValue)
		{
			RogueASC->ApplyModToAttribute(GetShieldAttribute(), EGameplayModOp::Override, NewValue);
		}
	}
}

void URogueAttributeSet::PostGameplayEffectExecute(const FGameplayEffectModCallbackData& Data)
{
	if (Data.EvaluatedData.Attribute == GetHealthAttribute())
	{
		// Do not allow health to go negative.
		SetHealth(FMath::Clamp(GetHealth(), 0.f, GetMaxHealth()));
		HandleHealthChange(Data);
	}
	else if (Data.EvaluatedData.Attribute == GetMaxHealthAttribute())
	{
		// Do not allow max health to drop below 1.
		SetMaxHealth(FMath::Max(GetMaxHealth(), 1.0f));
	}
	else if (Data.EvaluatedData.Attribute == GetShieldAttribute())
	{
		// Do not allow shield to go negative.
		SetShield(FMath::Clamp(GetShield(), 0.f, GetMaxShield()));
		HandleShieldChange(Data);
	}
	else if (Data.EvaluatedData.Attribute == GetBonusHealthAttribute())
	{
		// Do not allow bonus health to go negative.
		SetBonusHealth(FMath::Clamp(GetBonusHealth(), 0.f, GetMaxHealth()));
		HandleBonusHealthChange(Data);
	}
	else if (Data.EvaluatedData.Attribute == GetMaxShieldAttribute())
	{
		SetMaxShield(FMath::Max(GetMaxShield(), 0.0f));
	}
}

void URogueAttributeSet::OnRep_Health(const FGameplayAttributeData& OldValue)
{
	GAMEPLAYATTRIBUTE_REPNOTIFY(URogueAttributeSet, Health, OldValue);
}

void URogueAttributeSet::OnRep_Shield(const FGameplayAttributeData& OldValue)
{
	GAMEPLAYATTRIBUTE_REPNOTIFY(URogueAttributeSet, Shield, OldValue);
}

void URogueAttributeSet::OnRep_BonusHealth(const FGameplayAttributeData& OldValue)
{
	GAMEPLAYATTRIBUTE_REPNOTIFY(URogueAttributeSet, BonusHealth, OldValue);
}

void URogueAttributeSet::OnRep_MaxShield(const FGameplayAttributeData& OldValue)
{
	GAMEPLAYATTRIBUTE_REPNOTIFY(URogueAttributeSet, MaxShield, OldValue);
}

void URogueAttributeSet::OnRep_Damage(const FGameplayAttributeData& OldValue)
{
	GAMEPLAYATTRIBUTE_REPNOTIFY(URogueAttributeSet, Damage, OldValue);
}

void URogueAttributeSet::OnRep_AttackSpeed(const FGameplayAttributeData& OldValue)
{
	GAMEPLAYATTRIBUTE_REPNOTIFY(URogueAttributeSet, AttackSpeed, OldValue);
}

void URogueAttributeSet::OnRep_MoveSpeed(const FGameplayAttributeData& OldValue)
{
	GAMEPLAYATTRIBUTE_REPNOTIFY(URogueAttributeSet, MoveSpeed, OldValue);
}

void URogueAttributeSet::OnRep_CriticalChance(const FGameplayAttributeData& OldValue)
{
	GAMEPLAYATTRIBUTE_REPNOTIFY(URogueAttributeSet, CriticalChance, OldValue);
}

void URogueAttributeSet::OnRep_BlockChance(const FGameplayAttributeData& OldValue)
{
	GAMEPLAYATTRIBUTE_REPNOTIFY(URogueAttributeSet, BlockChance, OldValue);
}

void URogueAttributeSet::OnRep_SprintSpeed(const FGameplayAttributeData& OldValue)
{
	GAMEPLAYATTRIBUTE_REPNOTIFY(URogueAttributeSet, SprintSpeed, OldValue);
}

void URogueAttributeSet::OnRep_LifeStealOnHit(const FGameplayAttributeData& OldValue)
{
	GAMEPLAYATTRIBUTE_REPNOTIFY(URogueAttributeSet, LifeStealOnHit, OldValue);
}

void URogueAttributeSet::OnRep_LifeStealOnCritical(const FGameplayAttributeData& OldValue)
{
	GAMEPLAYATTRIBUTE_REPNOTIFY(URogueAttributeSet, LifeStealOnCritical, OldValue);
}

void URogueAttributeSet::OnRep_HealthRegenerationRate(const FGameplayAttributeData& OldValue)
{
	GAMEPLAYATTRIBUTE_REPNOTIFY(URogueAttributeSet, HealthRegenerationRate, OldValue);
}

void URogueAttributeSet::OnRep_ShieldRegenerationRate(const FGameplayAttributeData& OldValue)
{
	GAMEPLAYATTRIBUTE_REPNOTIFY(URogueAttributeSet, ShieldRegenerationRate, OldValue);
}

void URogueAttributeSet::OnRep_MaxHealth(const FGameplayAttributeData& OldValue)
{
	GAMEPLAYATTRIBUTE_REPNOTIFY(URogueAttributeSet, MaxHealth, OldValue);
}

void URogueAttributeSet::OnRep_IncomingDamage(const FGameplayAttributeData& OldValue)
{
	GAMEPLAYATTRIBUTE_REPNOTIFY(URogueAttributeSet, IncomingDamage, OldValue);
}

void URogueAttributeSet::ClampAttribute(const FGameplayAttribute& Attribute, float& NewValue) const
{
	if (Attribute == GetHealthAttribute())
	{
		// Do not allow health to go negative or above max health.
		NewValue = FMath::Clamp(NewValue, 0.0f, GetMaxHealth());
	}
	else if (Attribute == GetMaxHealthAttribute())
	{
		// Do not allow max health to drop below 1.
		NewValue = FMath::Max(NewValue, 1.0f);
	}
	else if (Attribute == GetShieldAttribute())
	{
		// Do not allow shield to go negative or above max shield.
		NewValue = FMath::Clamp(NewValue, 0.0f, GetMaxShield());
	}
	else if (Attribute == GetMaxShieldAttribute())
	{
		NewValue = FMath::Clamp(NewValue, 0.0f, GetMaxHealth());
	}
	else if (Attribute == GetBonusHealthAttribute())
	{
		NewValue = FMath::Clamp(NewValue, 0.0f, GetMaxHealth());
	}
}

URogueAbilitySystemComponent* URogueAttributeSet::GetRogueASC() const
{
	return CastChecked<URogueAbilitySystemComponent>(GetOwningAbilitySystemComponent());
}

void URogueAttributeSet::SetEffectProperties(const FGameplayEffectModCallbackData& Data, FEffectProperties& Props)
{
	Props.ContextHandle = Data.EffectSpec.GetContext();
	if (UAbilitySystemComponent* SourceASC = Props.ContextHandle.GetInstigatorAbilitySystemComponent())
	{
		Props.SourceASC = SourceASC;
		if (SourceASC->AbilityActorInfo.IsValid() && SourceASC->AbilityActorInfo->AvatarActor.IsValid())
		{
			Props.SourceAvatarActor = SourceASC->AbilityActorInfo->AvatarActor.Get();
			Props.SourceController = SourceASC->AbilityActorInfo->PlayerController.Get();
			if (Props.SourceController == nullptr && Props.SourceAvatarActor)
			{
				if (APawn* Pawn = Cast<APawn>(Props.SourceAvatarActor))
				{
					Props.SourceController = Pawn->GetController();
				}
			}
			if (Props.SourceController)
			{
				Props.SourceCharacter = Cast<ACharacter>(Props.SourceController->GetPawn());
			}
		}
	}

	Props.TargetAvatarActor = Data.Target.AbilityActorInfo->AvatarActor.Get();
	Props.TargetController = Data.Target.AbilityActorInfo->PlayerController.Get();
	Props.TargetCharacter = Cast<ACharacter>(Props.TargetAvatarActor);
	Props.TargetASC = UAbilitySystemBlueprintLibrary::GetAbilitySystemComponent(Props.TargetAvatarActor);
}

void URogueAttributeSet::HandleShieldChange(const FGameplayEffectModCallbackData& Data)
{
	const float CurrentShield = GetShield();
	const float CurrentMaxShield = GetMaxShield();

	if (CurrentShield <= 0.f && CurrentMaxShield > 0.f)
	{
		// Do something when shield is broken
	}
	else
	{
		
	}

	bool bNeedRegeneration = CurrentShield < CurrentMaxShield && !ShieldRegenHandle.IsValid();
	if (bNeedRegeneration)
	{
		// Start shield regeneration
		ShieldRegenHandle = URogueAbilitySystemLibrary::ApplyShieldRegenEffect(GetRogueASC());
	}
	else if (CurrentShield >= CurrentMaxShield && ShieldRegenHandle.IsValid())
	{
		// Stop shield regeneration
		GetRogueASC()->RemoveActiveGameplayEffect(ShieldRegenHandle);
		ShieldRegenHandle.Invalidate();
	}
	
}

void URogueAttributeSet::HandleHealthChange(const FGameplayEffectModCallbackData& Data)
{
	const float CurrentHealth = GetHealth();
	const float CurrentMaxHealth = GetMaxHealth();

	if (CurrentHealth <= 0.f && CurrentMaxHealth > 0.f)
	{
		// Do something when die
	}
	else
	{
		
	}

	bool bNeedRegeneration = CurrentHealth < CurrentMaxHealth && !HealthRegenHandle.IsValid();
	if (bNeedRegeneration)
	{
		// Start shield regeneration
		HealthRegenHandle = URogueAbilitySystemLibrary::ApplyHealthRegenEffect(GetRogueASC());
	}
	else if (CurrentHealth >= CurrentMaxHealth && HealthRegenHandle.IsValid())
	{
		// Stop shield regeneration
		GetRogueASC()->RemoveActiveGameplayEffect(HealthRegenHandle);
		HealthRegenHandle.Invalidate();
	}
}

void URogueAttributeSet::HandleBonusHealthChange(const FGameplayEffectModCallbackData& Data)
{
	const float CurrentBonusHealth = GetBonusHealth();

	bool bNeedDegenration = CurrentBonusHealth > 0.f && !BonusHealthDegenHandle.IsValid();
	if (bNeedDegenration)
	{
		// Start bonus health degeneration
		BonusHealthDegenHandle = URogueAbilitySystemLibrary::ApplyBonusHealthDegenEffect(GetRogueASC());
	}
	else if (CurrentBonusHealth <= 0.f && BonusHealthDegenHandle.IsValid())
	{
		// Stop bonus health degeneration
		GetRogueASC()->RemoveActiveGameplayEffect(BonusHealthDegenHandle);
		BonusHealthDegenHandle.Invalidate();
	}
}

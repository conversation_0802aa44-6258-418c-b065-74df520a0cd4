// Fill out your copyright notice in the Description page of Project Settings.


#include "AbilitySystem/RogueAbilitySystemLibrary.h"

#include "AbilitySystemBlueprintLibrary.h"
#include "GameplayEffect.h"
#include "GenericTeamAgentInterface.h"
#include "RogueLogChannel.h"
#include "RogueTypes.h"
#include "AbilitySystem/RogueAbilitySet.h"
#include "AbilitySystem/RogueAbilitySystemComponent.h"
#include "AbilitySystem/RogueAbilityTypes.h"
#include "AbilitySystem/RogueAttributeSet.h"
#include "Character/EnemyCharacterBase.h"
#include "Game/RogueGameplayTags.h"
#include "Game/RogueGameState.h"
#include "Game/RogueGlobalDataAsset.h"

void URogueAbilitySystemLibrary::GiveAbilitiesToASC(URogueAbilitySystemComponent* ASC,
                                                    const URogueAbilitySet* AbilitySet, UObject* SourceObject,
                                                    FAbilitySpecContainer& OutAbilitySpecContainer)
{
	if (!ASC || !AbilitySet)
	{
		UE_LOG(LogRogue, Error,
		       TEXT("URogueAbilitySystemLibrary::GiveAbilitiesToASC called with invalid ASC or AbilitySet"));
		return;
	}

	AbilitySet->GiveAbilitiesToASC(ASC, SourceObject, &OutAbilitySpecContainer);
}

bool URogueAbilitySystemLibrary::ApplyDamageInstant(const FRogueDamageParams& DamageParams)
{
	float Damage = DamageParams.Damage;
	AActor* TargetActor = DamageParams.TargetActor;
	AActor* DamageCauser = DamageParams.DamageCauser;

	URogueAbilitySystemComponent* TargetASC = GetRogueAbilitySystemComponent(TargetActor);
	if (!TargetASC)
	{
		UE_LOG(LogRogue, Error, TEXT("URogueAbilitySystemLibrary::ApplyDamageInstant called with invalid ASC"));
		return false;
	}
	URogueAbilitySystemComponent* SourceASC = GetRogueAbilitySystemComponent(DamageCauser);
	if (!SourceASC)
	{
		UE_LOG(LogRogue, Error, TEXT("URogueAbilitySystemLibrary::ApplyDamageInstant called with invalid ASC"));
		return false;
	}

	TSubclassOf<UGameplayEffect> DamageEffectClass = GetGlobalDataAsset(SourceASC)->DamageEffectClass;
	check(DamageEffectClass);

	FGameplayEffectContextHandle EffectContextHandle = SourceASC->MakeEffectContext();
	EffectContextHandle.AddSourceObject(DamageCauser);


	// == 应用伤害
	FGameplayEffectSpecHandle SpecHandle = SourceASC->MakeOutgoingSpec(
		DamageEffectClass, 1.f, EffectContextHandle
	);
	check(SpecHandle.IsValid());
	SpecHandle.Data->SetSetByCallerMagnitude(RogueGameplayTags::MetaAttribute_Damage, Damage);
	SourceASC->ApplyGameplayEffectSpecToTarget(*SpecHandle.Data.Get(), TargetASC);


	return true;
}

bool URogueAbilitySystemLibrary::MakeDamageParams(float Damage, AActor* TargetActor, AActor* DamageCauser,
                                                  FRogueDamageParams& OutDamageParams)
{
	FRogueDamageParams DamageParams;
	DamageParams.Damage = Damage;
	DamageParams.TargetActor = TargetActor;
	DamageParams.DamageCauser = DamageCauser;
	OutDamageParams = DamageParams;
	return true;
}

FActiveGameplayEffectHandle URogueAbilitySystemLibrary::SimpleApplyGameplayEffectToSelf(
	URogueAbilitySystemComponent* ASC, TSubclassOf<UGameplayEffect> EffectClass)
{
	FGameplayEffectContextHandle EffectContextHandle = ASC->MakeEffectContext();
	EffectContextHandle.AddSourceObject(ASC->GetOwner());
	FGameplayEffectSpecHandle SpecHandle = ASC->MakeOutgoingSpec(EffectClass, 1.f,
	                                                             EffectContextHandle);
	return ASC->ApplyGameplayEffectSpecToSelf(*SpecHandle.Data.Get());
}

FActiveGameplayEffectHandle URogueAbilitySystemLibrary::ApplyShieldRegenEffect(URogueAbilitySystemComponent* ASC)
{
	return SimpleApplyGameplayEffectToSelf(ASC, GetGlobalDataAsset(ASC)->ShieldRegenEffectClass);
}

FActiveGameplayEffectHandle URogueAbilitySystemLibrary::ApplyHealthRegenEffect(URogueAbilitySystemComponent* ASC)
{
	return SimpleApplyGameplayEffectToSelf(ASC, GetGlobalDataAsset(ASC)->HealthRegenEffectClass);
}

FActiveGameplayEffectHandle URogueAbilitySystemLibrary::ApplyBonusHealthDegenEffect(URogueAbilitySystemComponent* ASC)
{
	return SimpleApplyGameplayEffectToSelf(ASC, GetGlobalDataAsset(ASC)->BonusHealthDegenEffectClass);
}

FActiveGameplayEffectHandle URogueAbilitySystemLibrary::ApplyDamagedRecentlyEffect(URogueAbilitySystemComponent* ASC)
{
	return SimpleApplyGameplayEffectToSelf(ASC, GetGlobalDataAsset(ASC)->RecentlyDamagedEffectClass);
}

FActiveGameplayEffectHandle URogueAbilitySystemLibrary::ApplyHealInstantEffect(URogueAbilitySystemComponent* ASC,
                                                                               float Amount)
{
	FGameplayEffectContextHandle EffectContextHandle = ASC->MakeEffectContext();
	EffectContextHandle.AddSourceObject(ASC->GetOwner());
	FGameplayEffectSpecHandle SpecHandle = ASC->MakeOutgoingSpec(GetGlobalDataAsset(ASC)->HealInstantEffectClass, 1.f,
	                                                             EffectContextHandle);
	SpecHandle.Data->SetSetByCallerMagnitude(RogueGameplayTags::Attribute_Health, Amount);
	return ASC->ApplyGameplayEffectSpecToSelf(*SpecHandle.Data.Get());
}

FActiveGameplayEffectHandle URogueAbilitySystemLibrary::ApplyBonusHealthAddEffect(URogueAbilitySystemComponent* ASC,
	float Amount)
{
	FGameplayEffectContextHandle EffectContextHandle = ASC->MakeEffectContext();
	EffectContextHandle.AddSourceObject(ASC->GetOwner());
	FGameplayEffectSpecHandle SpecHandle = ASC->MakeOutgoingSpec(GetGlobalDataAsset(ASC)->BonusHealthAddEffectClass,
	                                                             1.f,
	                                                             EffectContextHandle);
	SpecHandle.Data->SetSetByCallerMagnitude(RogueGameplayTags::Attribute_BonusHealth, Amount);
	return ASC->ApplyGameplayEffectSpecToSelf(*SpecHandle.Data.Get());
}

URogueGlobalDataAsset* URogueAbilitySystemLibrary::GetGlobalDataAsset(UObject* WorldContextObject)
{
	UWorld* World = GEngine->GetWorldFromContextObject(WorldContextObject, EGetWorldErrorMode::LogAndReturnNull);
	check(World);

	return World->GetGameState<ARogueGameState>()->GlobalDataAsset;
}

bool URogueAbilitySystemLibrary::IsTargetFriendly(AActor* Source, AActor* Target)
{
	return FGenericTeamId::GetAttitude(Source, Target) == ETeamAttitude::Friendly;
}

URogueAbilitySystemComponent* URogueAbilitySystemLibrary::GetRogueAbilitySystemComponent(AActor* Actor)
{
	UAbilitySystemComponent* ASC = UAbilitySystemBlueprintLibrary::GetAbilitySystemComponent(Actor);
	if (!ASC) return nullptr;
	return CastChecked<URogueAbilitySystemComponent>(ASC);
}

EEnemyClass URogueAbilitySystemLibrary::GetEnemyClassFromSelfGameplayEffectSpec(const FGameplayEffectSpec& Spec)
{
	FGameplayEffectContextHandle Context = Spec.GetContext();
	URogueAbilitySystemComponent* TargetRogueASC = Cast<URogueAbilitySystemComponent>(
		Context.GetInstigatorAbilitySystemComponent());
	check(TargetRogueASC);
	AActor* TargetActor = TargetRogueASC->GetAvatarActor();
	check(TargetActor);
	AEnemyCharacterBase* TargetEnemy = Cast<AEnemyCharacterBase>(TargetActor);
	check(TargetEnemy);
	return TargetEnemy->GetEnemyClass();
}

URogueAttributeSet* URogueAbilitySystemLibrary::GetRogueAttributeSet(AActor* Actor)
{
	ARogueCharacterBase* RogueCharacter = Cast<ARogueCharacterBase>(Actor);
	if (!RogueCharacter) return nullptr;
	return RogueCharacter->GetRogueAS();
}

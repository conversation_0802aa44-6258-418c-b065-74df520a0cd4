// Fill out your copyright notice in the Description page of Project Settings.


#include "Actor/WeaponBase.h"

#include "AbilitySystemBlueprintLibrary.h"
#include "RogueLogChannel.h"
#include "AbilitySystem/RogueAbilitySystemComponent.h"
#include "Character/RogueCharacterBase.h"
#include "Components/BoxComponent.h"
#include "Game/RogueGameplayTags.h"
#include "Kismet/GameplayStatics.h"
#include "Kismet/KismetSystemLibrary.h"

AWeaponBase::AWeaponBase()
{
	WeaponRoot = CreateDefaultSubobject<USceneComponent>("Root");
	SetRootComponent(WeaponRoot);
	bReplicates = true;

	WeaponMesh = CreateDefaultSubobject<UStaticMeshComponent>("WeaponMesh");
	WeaponMesh->SetupAttachment(WeaponRoot);
	WeaponMesh->SetCollisionEnabled(ECollisionEnabled::Type::NoCollision);

	WeaponCollision = CreateDefaultSubobject<UBoxComponent>("WeaponCollision");
	WeaponCollision->SetupAttachment(WeaponRoot);
	WeaponCollision->SetCollisionEnabled(ECollisionEnabled::Type::QueryOnly);
	WeaponCollision->SetCollisionResponseToAllChannels(ECR_Ignore);
	WeaponCollision->SetCollisionResponseToChannel(ECC_Pawn, ECR_Overlap);
}

void AWeaponBase::EnableCollision()
{
	if (IsValid(WeaponCollision) && WeaponCollision->GetBodyInstance() && WeaponCollision->GetBodyInstance()->IsValidBodyInstance())
	{
		WeaponCollision->SetCollisionEnabled(ECollisionEnabled::Type::QueryOnly);
	}
}

void AWeaponBase::DisableCollision()
{
	if (IsValid(WeaponCollision))
	{
		WeaponCollision->SetCollisionEnabled(ECollisionEnabled::Type::NoCollision);
	}
}

void AWeaponBase::BeginPlay()
{
	Super::BeginPlay();

	WeaponCollision->OnComponentBeginOverlap.AddDynamic(this, &ThisClass::OnBeginOverlap);
	WeaponCollision->OnComponentEndOverlap.AddDynamic(this, &ThisClass::OnEndOverlap);

	WeaponCollision->SetCollisionEnabled(ECollisionEnabled::Type::NoCollision);
}

void AWeaponBase::OnBeginOverlap(UPrimitiveComponent* OverlappedComp, AActor* Other, UPrimitiveComponent* OtherComp,
                                 int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult)
{
	FGameplayEventData Payload;
	Payload.EventTag = RogueGameplayTags::GameplayEvent_Attack;
	Payload.Instigator = GetOwner();
	Payload.Target = Other;

	if (Other == GetOwner()) return;

	ARogueCharacterBase* RogueCharacterBase = Cast<ARogueCharacterBase>(GetOwner());
	RogueCharacterBase->GetRogueASC()->SendGameplayEventToSelf(RogueGameplayTags::GameplayEvent_Attack, Payload);
}

void AWeaponBase::OnEndOverlap(UPrimitiveComponent* OverlappedComp, AActor* Other, UPrimitiveComponent* OtherComp,
                               int32 OtherBodyIndex)
{
	UE_LOG(LogRogue, Warning, TEXT("EndOverlap, actor: %s"), *Other->GetName());
}

// Fill out your copyright notice in the Description page of Project Settings.


#include "Character/PlayerCharacterBase.h"

#include "RogueLogChannel.h"
#include "AbilitySystem/RogueAbilitySystemComponent.h"
#include "AbilitySystem/RogueAttributeSet.h"
#include "Game/RogueGameState.h"
#include "Game/RogueGlobalDataAsset.h"
#include "Player/RoguePlayerState.h"
#include "UI/MVVM_InGameActor.h"
#include "UI/RogueUserWidget.h"
#include "UI/HUD/RogueHUD.h"

APlayerCharacterBase::APlayerCharacterBase()
{
	PrimaryActorTick.bCanEverTick = true;
}

void APlayerCharacterBase::PossessedBy(AController* NewController)
{
	Super::PossessedBy(NewController);

	Initialize();
}

void APlayerCharacterBase::OnRep_PlayerState()
{
	Super::OnRep_PlayerState();

	Initialize();
}

void APlayerCharacterBase::OnHealthChange(const FOnAttributeChangeData& Data)
{
	const float CurrentHealth = Data.NewValue;
	UE_LOG(LogTemp, Warning, TEXT("OnHealthChange New=%f, in %s"), Data.NewValue,
	       HasAuthority() ? TEXT("Server") : TEXT("Client"));
	if (!GetRogueHUDWidget()) return;
	if (UMVVM_InGameActor* InGameViewModel = Cast<UMVVM_InGameActor>(GetRogueHUDWidget()->GetSecondaryViewModel()))
	{
		const float MaxHealth = GetRogueASC()->GetNumericAttribute(URogueAttributeSet::GetMaxHealthAttribute());
		InGameViewModel->PushRawHealth(CurrentHealth, MaxHealth);
		UE_LOG(LogTemp, Warning, TEXT("Start Interpolate %f in %s"), Data.NewValue,
		       HasAuthority() ? TEXT("Server") : TEXT("Client"));
	}
}

void APlayerCharacterBase::OnBonusHealthChange(const FOnAttributeChangeData& Data)
{
	const float CurrentBonusHealth = Data.NewValue;

	if (!GetRogueHUDWidget()) return;
	if (UMVVM_InGameActor* InGameViewModel = Cast<UMVVM_InGameActor>(GetRogueHUDWidget()->GetSecondaryViewModel()))
	{
		const float MaxHealth = GetRogueASC()->GetNumericAttribute(URogueAttributeSet::GetMaxHealthAttribute());
		InGameViewModel->PushRawBonus(CurrentBonusHealth, MaxHealth);
	}
}

void APlayerCharacterBase::OnShieldChange(const FOnAttributeChangeData& Data)
{
	const float CurrentShield = Data.NewValue;

	if (!GetRogueHUDWidget()) return;
	if (UMVVM_InGameActor* InGameViewModel = Cast<UMVVM_InGameActor>(GetRogueHUDWidget()->GetSecondaryViewModel()))
	{
		const float MaxShield = GetRogueASC()->GetNumericAttribute(URogueAttributeSet::GetMaxShieldAttribute());
		InGameViewModel->PushRawShield(CurrentShield, MaxShield);
	}
}

void APlayerCharacterBase::BeginPlay()
{
	Super::BeginPlay();

	SetGenericTeamId(1);
}

void APlayerCharacterBase::Tick(float DeltaSeconds)
{
	Super::Tick(DeltaSeconds);

}

void APlayerCharacterBase::InitAbilityActorInfo()
{
	ARoguePlayerState* RoguePlayerState = GetPlayerState<ARoguePlayerState>();
	check(RoguePlayerState);
	AbilitySystemComponent = RoguePlayerState->GetAbilitySystemComponent();
	AttributeSet = RoguePlayerState->GetAttributeSet();

	GetRogueASC()->InitAbilityActorInfo(RoguePlayerState, this);
}

void APlayerCharacterBase::Initialize()
{
	ARogueGameState* RogueGameState = GetWorld()->GetGameState<ARogueGameState>();
	check(RogueGameState);

	InitAbilityActorInfo();
	GiveStartupAbilities();
	InitOverlay();
	BindASCDelegates();
	GiveStartupAttributes(RogueGameState->GlobalDataAsset->StartupPlayerAttributeEffectClass);
}

void APlayerCharacterBase::InitOverlay()
{
	APlayerController* PC = Cast<APlayerController>(GetController());
	if (!PC) return;
	if (PC->IsLocalController())
	{
		ARogueHUD* RogueHUD = Cast<ARogueHUD>(PC->GetHUD());
		if (RogueHUD)
		{
			RogueHUD->InitOverlay();
		}
	}
}

URogueUserWidget* APlayerCharacterBase::GetRogueHUDWidget() const
{
	APlayerController* PC = Cast<APlayerController>(GetController());
	if (PC)
	{
		ARogueHUD* RogueHUD = Cast<ARogueHUD>(PC->GetHUD());
		if (RogueHUD)
		{
			return RogueHUD->GetOverlayWidget();
		}
	}
	return nullptr;
}

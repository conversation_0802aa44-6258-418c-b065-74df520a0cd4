// Fill out your copyright notice in the Description page of Project Settings.


#include "Character/RogueCharacterBase.h"

#include "AbilitySystem/RogueAbilitySlotComponent.h"
#include "AbilitySystem/RogueAbilitySystemComponent.h"
#include "AbilitySystem/RogueAttributeSet.h"
#include "Actor/WeaponBase.h"
#include "ActorComponent/CombatComponent.h"
#include "Character/RoguePawnData.h"
#include "Components/CapsuleComponent.h"
#include "Game/RogueGameplayTags.h"
#include "Game/RogueGameState.h"
#include "Game/RogueGlobalDataAsset.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "Net/UnrealNetwork.h"
#include "UI/MVVM_InGameActor.h"
#include "UI/WIdgetComponent/RogueWidgetComponentBase.h"


ARogueCharacterBase::ARogueCharacterBase()
{
	PrimaryActorTick.bCanEverTick = false;

	AbilitySlotComponent = CreateDefaultSubobject<URogueAbilitySlotComponent>("AbilitySlotComponent");
	AbilitySlotComponent->SetIsReplicated(true);

	InGameWidget = CreateDefaultSubobject<URogueWidgetComponentBase>("InGameWidget");
	InGameWidget->SetupAttachment(RootComponent);
	InGameWidget->SetCollisionEnabled(ECollisionEnabled::NoCollision);
	InGameWidget->SetCollisionResponseToAllChannels(ECR_Ignore);

	MyTeamID = FGenericTeamId::NoTeam;
}

UAbilitySystemComponent* ARogueCharacterBase::GetAbilitySystemComponent() const
{
	return AbilitySystemComponent;
}

UAttributeSet* ARogueCharacterBase::GetAttributeSet() const
{
	return AttributeSet;
}

URogueAbilitySystemComponent* ARogueCharacterBase::GetRogueASC()
{
	return CastChecked<URogueAbilitySystemComponent>(GetAbilitySystemComponent());
}

URogueAttributeSet* ARogueCharacterBase::GetRogueAS() const
{
	return CastChecked<URogueAttributeSet>(GetAttributeSet());
}

void ARogueCharacterBase::EnableWeaponCollision_Implementation()
{
	for (AWeaponBase* Weapon : EquippedWeapons)
	{
		Weapon->EnableCollision();
	}
}

void ARogueCharacterBase::DisableWeaponCollision_Implementation()
{
	for (AWeaponBase* Weapon : EquippedWeapons)
	{
		Weapon->DisableCollision();
	}
}

void ARogueCharacterBase::Die_Implementation()
{
	ICombatInterface::Die_Implementation();
}

int32 ARogueCharacterBase::GetLevel_Implementation()
{
	return Level;
}

void ARogueCharacterBase::SetGenericTeamId(const FGenericTeamId& TeamID)
{
	MyTeamID = TeamID;
}

void ARogueCharacterBase::BeginPlay()
{
	Super::BeginPlay();
	SpawnWeapons();
}

void ARogueCharacterBase::GiveStartupAbilities()
{
	if (!RoguePawnData) return;
	AbilitySlotComponent->GiveAbilitiesToASC(GetRogueASC(), RoguePawnData->AbilitySet);
}

void ARogueCharacterBase::GiveStartupAttributes(TSubclassOf<UGameplayEffect> EffectClassOverride)
{
	if (!RoguePawnData) return;
	ARogueGameState* RogueGameState = GetWorld()->GetGameState<ARogueGameState>();
	check(RogueGameState);

	TSubclassOf<UGameplayEffect> EffectClass = RogueGameState->GlobalDataAsset->StartupAttributeEffectClass;
	if (EffectClassOverride) EffectClass = EffectClassOverride;

	FGameplayEffectContextHandle EffectContextHandle = GetRogueASC()->MakeEffectContext();
	EffectContextHandle.AddSourceObject(this);
	FGameplayEffectSpecHandle SpecHandle = GetRogueASC()->MakeOutgoingSpec(EffectClass
	                                                                       , 1.f, EffectContextHandle
	);
	check(SpecHandle.IsValid());
	GetRogueASC()->ApplyGameplayEffectSpecToSelf(*SpecHandle.Data.Get());

	ApplyVitalAttribute();
}

void ARogueCharacterBase::BindASCDelegates()
{
	GetRogueASC()->GetGameplayAttributeValueChangeDelegate(URogueAttributeSet::GetHealthAttribute()).AddUObject(
		this, &ThisClass::OnHealthChange);
	GetRogueASC()->GetGameplayAttributeValueChangeDelegate(URogueAttributeSet::GetShieldAttribute()).AddUObject(
		this, &ThisClass::OnShieldChange);
	GetRogueASC()->GetGameplayAttributeValueChangeDelegate(URogueAttributeSet::GetBonusHealthAttribute()).AddUObject(
		this, &ThisClass::OnBonusHealthChange);

	GetRogueASC()->GetGameplayAttributeValueChangeDelegate(URogueAttributeSet::GetMoveSpeedAttribute()).AddUObject(
		this, &ThisClass::OnMoveSpeedChange);

	GetRogueASC()->RegisterGameplayTagEvent(RogueGameplayTags::StateTag_Movement_Sprinting).AddUObject(
		this, &ThisClass::OnSprintTagChange);

#if WITH_CLIENT_CODE
	// 补发，防止客户端收不到
	FOnAttributeChangeData Init;
	Init.OldValue = Init.NewValue = GetRogueASC()->GetNumericAttribute(URogueAttributeSet::GetHealthAttribute());
	OnHealthChange(Init);
	Init.OldValue = Init.NewValue = GetRogueASC()->GetNumericAttribute(URogueAttributeSet::GetShieldAttribute());
	OnShieldChange(Init);
	Init.OldValue = Init.NewValue = GetRogueASC()->GetNumericAttribute(URogueAttributeSet::GetBonusHealthAttribute());
	OnBonusHealthChange(Init);
	Init.OldValue = Init.NewValue = GetRogueASC()->GetNumericAttribute(URogueAttributeSet::GetMoveSpeedAttribute());
	OnMoveSpeedChange(Init);
#endif
}

void ARogueCharacterBase::ReCalculateMaxWalkSpeed()
{
	float MoveSpeed = GetRogueASC()->GetNumericAttribute(URogueAttributeSet::GetMoveSpeedAttribute());
	float SprintSpeed = GetRogueASC()->GetNumericAttribute(URogueAttributeSet::GetSprintSpeedAttribute());
	if (bSprinting)
	{
		MoveSpeed *= SprintSpeed;
	}
	GetCharacterMovement()->MaxWalkSpeed = MoveSpeed;
}

void ARogueCharacterBase::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
	Super::GetLifetimeReplicatedProps(OutLifetimeProps);
	DOREPLIFETIME(ARogueCharacterBase, bSprinting);
}

void ARogueCharacterBase::OnHealthChange(const FOnAttributeChangeData& Data)
{
	const float CurrentHealth = Data.NewValue;

	if (UMVVM_InGameActor* InGameViewModel = Cast<UMVVM_InGameActor>(InGameWidget->GetViewModel()))
	{
		const float MaxHealth = GetRogueASC()->GetNumericAttribute(URogueAttributeSet::GetMaxHealthAttribute());
		InGameViewModel->PushRawHealth(CurrentHealth, MaxHealth);
	}
}

void ARogueCharacterBase::OnShieldChange(const FOnAttributeChangeData& Data)
{
	const float CurrentShield = Data.NewValue;

	if (UMVVM_InGameActor* InGameViewModel = Cast<UMVVM_InGameActor>(InGameWidget->GetViewModel()))
	{
		const float MaxShield = GetRogueASC()->GetNumericAttribute(URogueAttributeSet::GetMaxShieldAttribute());
		InGameViewModel->PushRawShield(CurrentShield, MaxShield);
	}
}

void ARogueCharacterBase::OnBonusHealthChange(const FOnAttributeChangeData& Data)
{
	const float CurrentBonusHealth = Data.NewValue;

	if (UMVVM_InGameActor* InGameViewModel = Cast<UMVVM_InGameActor>(InGameWidget->GetViewModel()))
	{
		const float MaxHealth = GetRogueASC()->GetNumericAttribute(URogueAttributeSet::GetMaxHealthAttribute());
		InGameViewModel->PushRawBonus(CurrentBonusHealth, MaxHealth);
	}
}

void ARogueCharacterBase::OnMoveSpeedChange(const FOnAttributeChangeData& Data)
{
	ReCalculateMaxWalkSpeed();
}

void ARogueCharacterBase::OnSprintTagChange(const FGameplayTag Tag, int32 NewCount)
{
	UE_LOG(LogTemp, Warning, TEXT("SprintTagChange in %s:"), HasAuthority() ? TEXT("Server") : TEXT("Client"));
	ReCalculateMaxWalkSpeed();
}

void ARogueCharacterBase::ApplyVitalAttribute()
{
	ARogueGameState* RogueGameState = GetWorld()->GetGameState<ARogueGameState>();
	check(RogueGameState);

	FGameplayEffectContextHandle EffectContextHandle = GetRogueASC()->MakeEffectContext();
	EffectContextHandle.AddSourceObject(this);
	FGameplayEffectSpecHandle SpecHandle = GetRogueASC()->MakeOutgoingSpec(
		RogueGameState->GlobalDataAsset->StartupVitalAttributeEffectClass, 1.f, EffectContextHandle
	);
	check(SpecHandle.IsValid());
	GetRogueASC()->ApplyGameplayEffectSpecToSelf(*SpecHandle.Data.Get());
}


void ARogueCharacterBase::SpawnWeapons()
{
	if (!RoguePawnData) return;
	for (const FCharacterWeaponData& WeaponData : RoguePawnData->WeaponsDataToSpawn)
	{
		if (WeaponData.WeaponClass && WeaponData.SocketName != NAME_None)
		{
			EquipWeapon(WeaponData);
		}
	}
}

void ARogueCharacterBase::EquipWeapon(const FCharacterWeaponData& WeaponData)
{
	AWeaponBase* Weapon = GetWorld()->SpawnActorDeferred<AWeaponBase>(
		WeaponData.WeaponClass, FTransform::Identity, this);
	Weapon->SetReplicates(true);
	Weapon->FinishSpawning(FTransform::Identity, /*bIsDefaultTransform=*/ true);
	Weapon->SetActorRelativeTransform(WeaponData.OffsetTransform);
	Weapon->AttachToComponent(GetMesh(), FAttachmentTransformRules::KeepRelativeTransform,
	                          WeaponData.SocketName);

	EquippedWeapons.Add(Weapon);
}

void ARogueCharacterBase::UnequipWeapon()
{
	for (AWeaponBase* Weapon : EquippedWeapons)
	{
		Weapon->Destroy();
	}

	EquippedWeapons.Reset();
}

void ARogueCharacterBase::HandleHealthChange(const FOnAttributeChangeData& Data)
{
	OnHealthChange(Data);
}

void ARogueCharacterBase::HandleShieldChange(const FOnAttributeChangeData& Data)
{
	OnShieldChange(Data);
}

void ARogueCharacterBase::HandleBonusHealthChange(const FOnAttributeChangeData& Data)
{
	OnBonusHealthChange(Data);
}

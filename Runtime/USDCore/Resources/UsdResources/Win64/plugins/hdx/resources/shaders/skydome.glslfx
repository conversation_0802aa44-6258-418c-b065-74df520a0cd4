-- glslfx version 0.1

//
// Copyright 2021 Pixar
//
// Licensed under the terms set forth in the LICENSE.txt file available at
// https://openusd.org/license.
//

#import $TOOLS/hdSt/shaders/surfaceHelpers.glslfx

-- configuration
{
    "techniques": {
        "default": {
            "SkydomeFragment": {
                "source": [ "SurfaceHelpers.Lighting", 
                            "Skydome.Fragment" ]
            }
        }
    }
}

-- glsl Skydome.Fragment

const float farPlane = 1.0;

float wrapSampleValue(float value)
{
    if (value < 0.0) {
        value += 1.0;
    }
    else if (value > 1.0) {
        value -= 1.0;
    }
    return value;
}

vec2 getSampleCoord(vec3 sample3D)
{
    vec2 latLong = ProjectToLatLong(sample3D);
    return vec2(wrapSampleValue(latLong.x), wrapSampleValue(latLong.y));
}


void main(void)
{
    // Transform the UV coordinates into NDC space and place at the far plane
    // (z = 1) before transforming into view space.
    vec2 uvOut_ndc = (uvOut * vec2(2.0)) - vec2(1.0);
    vec4 uvOut_view = invProjMatrix * vec4(uvOut_ndc, farPlane, 1.0);

    // Normalize to use as the initial sampleDirection
    vec3 sampleDirection = normalize(uvOut_view.xyz);

    // Apply the camera rotation and lightTransform to the sampleDirection
    sampleDirection = 
        ( lightTransform * viewToWorld * vec4(sampleDirection, 0.0) ).xyz;
    
    // Sample Skydome Texture with the sampleDirection
    vec2 sampleCoord = getSampleCoord(sampleDirection);
    hd_FragColor = vec4(HgiGet_skydomeTexture(sampleCoord).xyz, 1.0);
    gl_FragDepth = farPlane;
}

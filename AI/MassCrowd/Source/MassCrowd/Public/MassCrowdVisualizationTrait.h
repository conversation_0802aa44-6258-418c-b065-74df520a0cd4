// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once 

#include "MassVisualizationTrait.h"
#include "MassCrowdVisualizationTrait.generated.h"

UCLASS(BlueprintType, EditInlineNew, CollapseCategories, meta=(DisplayName="Crowd Visualization"))
class MASSCROWD_API UMassCrowdVisualizationTrait : public UMassVisualizationTrait
{
	GENERATED_BODY()
public:
	UMassCrowdVisualizationTrait();

	virtual void BuildTemplate(FMassEntityTemplateBuildContext& BuildContext, const UWorld& World) const override;
};
